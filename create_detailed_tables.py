import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取分类结果
df = pd.read_csv('单位分类结果.csv', encoding='utf-8')

# 读取原始数据获取金额信息
df_original = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 处理金额数据
df_original['中标价格_清理'] = df_original['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df_original['中标价格_数值'] = pd.to_numeric(df_original['中标价格_清理'], errors='coerce')

# 合并数据
df_merged = df.merge(df_original[['项目标题', '中标价格_数值']], on='项目标题', how='left')

# 创建招标单位统计表
bidding_table = df_merged.groupby('招标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)

bidding_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
bidding_table = bidding_table.reset_index()
bidding_table['占比(%)'] = (bidding_table['项目数量'] / bidding_table['项目数量'].sum() * 100).round(1)

# 创建中标单位统计表
winning_table = df_merged.groupby('中标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)

winning_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
winning_table = winning_table.reset_index()
winning_table['占比(%)'] = (winning_table['项目数量'] / winning_table['项目数量'].sum() * 100).round(1)

# 按项目数量排序
bidding_table = bidding_table.sort_values('项目数量', ascending=False)
winning_table = winning_table.sort_values('项目数量', ascending=False)

print("=" * 80)
print("招标单位分类统计表")
print("=" * 80)
print(bidding_table.to_string(index=False))

print("\n" + "=" * 80)
print("中标单位分类统计表")
print("=" * 80)
print(winning_table.to_string(index=False))

# 保存表格到CSV
bidding_table.to_csv('招标单位统计表.csv', index=False, encoding='utf-8-sig')
winning_table.to_csv('中标单位统计表.csv', index=False, encoding='utf-8-sig')

# 创建更详细的可视化图表
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# 招标单位项目数量饼图
colors1 = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
wedges1, texts1, autotexts1 = ax1.pie(bidding_table['项目数量'], 
                                      labels=bidding_table['招标单位分类'],
                                      colors=colors1[:len(bidding_table)],
                                      autopct='%1.1f%%',
                                      startangle=90)
ax1.set_title('招标单位项目数量分布', fontsize=14, fontweight='bold')

# 招标单位金额分布饼图
wedges2, texts2, autotexts2 = ax2.pie(bidding_table['总金额(万元)'], 
                                      labels=bidding_table['招标单位分类'],
                                      colors=colors1[:len(bidding_table)],
                                      autopct='%1.1f%%',
                                      startangle=90)
ax2.set_title('招标单位金额分布', fontsize=14, fontweight='bold')

# 中标单位项目数量饼图
colors2 = ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43']
wedges3, texts3, autotexts3 = ax3.pie(winning_table['项目数量'], 
                                      labels=winning_table['中标单位分类'],
                                      colors=colors2[:len(winning_table)],
                                      autopct='%1.1f%%',
                                      startangle=90)
ax3.set_title('中标单位项目数量分布', fontsize=14, fontweight='bold')

# 中标单位金额分布饼图
wedges4, texts4, autotexts4 = ax4.pie(winning_table['总金额(万元)'], 
                                      labels=winning_table['中标单位分类'],
                                      colors=colors2[:len(winning_table)],
                                      autopct='%1.1f%%',
                                      startangle=90)
ax4.set_title('中标单位金额分布', fontsize=14, fontweight='bold')

plt.tight_layout()
plt.savefig('招投标单位详细分析图.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')

print(f"\n详细分析图表已保存为: 招投标单位详细分析图.png")
print(f"招标单位统计表已保存为: 招标单位统计表.csv")
print(f"中标单位统计表已保存为: 中标单位统计表.csv")

# 输出分析总结
print("\n" + "=" * 80)
print("分析总结")
print("=" * 80)

print("\n【招标单位特点】:")
print(f"1. 国企主导: 国企招标项目{bidding_table.iloc[0]['项目数量']}个，占比{bidding_table.iloc[0]['占比(%)']}%")
print(f"2. 政府参与: 政府招标项目{bidding_table[bidding_table['招标单位分类']=='政府']['项目数量'].iloc[0]}个，体现政策引导作用")
print(f"3. 高校积极: 高校招标{bidding_table[bidding_table['招标单位分类']=='高校']['项目数量'].iloc[0]}个项目，产学研结合明显")

print("\n【中标单位特点】:")
print(f"1. 国企优势明显: 中标{winning_table.iloc[0]['项目数量']}个项目，占比{winning_table.iloc[0]['占比(%)']}%")
print(f"2. 专业化分工: 测绘单位中标{winning_table[winning_table['中标单位分类']=='测绘单位']['项目数量'].iloc[0]}个项目，体现专业优势")
print(f"3. 运营商参与: 三大运营商中标{winning_table[winning_table['中标单位分类']=='三大运营商']['项目数量'].iloc[0]}个项目，通信技术支撑重要")
