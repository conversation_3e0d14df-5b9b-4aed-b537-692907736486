import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取CSV文件
df = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 清理数据，去除空值
df_clean = df.dropna(subset=['中标单位', '招标单位'])

def classify_bidding_unit(unit_name):
    """分类招标单位"""
    if pd.isna(unit_name):
        return '其他'
    
    unit_name = str(unit_name).strip()
    
    # 政府单位关键词
    gov_keywords = ['政府', '管委会', '管理委员会', '发改委', '交通局', '公安局', '气象局', 
                   '自然资源', '规划局', '街道', '镇政府', '区政府', '市政府', '县政府',
                   '发展和改革', '交通运输', '生态环境', '应急管理', '城管', '综合执法']
    
    # 国企关键词
    soe_keywords = ['集团', '建设', '投资', '发展', '控股', '置业', '产业园', '开发区',
                   '城投', '交投', '国投', '建投', '文旅', '科技城', '新城', '园区']
    
    # 三大运营商
    telecom_keywords = ['中国电信', '中国移动', '中国联通', '电信', '移动', '联通']
    
    # 高校关键词
    university_keywords = ['大学', '学院', '职业技术', '专业学校', '教育']
    
    # 检查三大运营商
    for keyword in telecom_keywords:
        if keyword in unit_name:
            return '三大运营商'
    
    # 检查政府
    for keyword in gov_keywords:
        if keyword in unit_name:
            return '政府'
    
    # 检查高校
    for keyword in university_keywords:
        if keyword in unit_name:
            return '高校'
    
    # 检查国企
    for keyword in soe_keywords:
        if keyword in unit_name:
            return '国企'
    
    return '其他'

def classify_winning_unit(unit_name):
    """分类中标单位"""
    if pd.isna(unit_name):
        return '其他'
    
    unit_name = str(unit_name).strip()
    
    # 国企关键词
    soe_keywords = ['集团', '建设', '工程', '建筑', '设计院', '咨询', '科工', '中建', 
                   '中交', '中铁', '中冶', '航天', '中航', '中电', '国电', '华能',
                   '中通服', '中邮建', '苏交科', '华设', '江苏', '建龙', '嘉盛']
    
    # 三大运营商
    telecom_keywords = ['中国电信', '中国移动', '中国联通', '电信', '移动', '联通']
    
    # 高校关键词
    university_keywords = ['大学', '学院', '航空航天', '科技大学']
    
    # 测绘单位关键词
    survey_keywords = ['测绘', '地理信息', '勘察', '规划设计', '建筑设计', '市政', '勘测']
    
    # 检查三大运营商
    for keyword in telecom_keywords:
        if keyword in unit_name:
            return '三大运营商'
    
    # 检查高校
    for keyword in university_keywords:
        if keyword in unit_name:
            return '高校'
    
    # 检查测绘单位
    for keyword in survey_keywords:
        if keyword in unit_name:
            return '测绘单位'
    
    # 检查国企
    for keyword in soe_keywords:
        if keyword in unit_name:
            return '国企'
    
    return '其他'

# 应用分类函数
df_clean['招标单位分类'] = df_clean['招标单位'].apply(classify_bidding_unit)
df_clean['中标单位分类'] = df_clean['中标单位'].apply(classify_winning_unit)

# 统计招标单位
bidding_stats = df_clean['招标单位分类'].value_counts().reset_index()
bidding_stats.columns = ['单位类型', '项目数量']

# 统计中标单位
winning_stats = df_clean['中标单位分类'].value_counts().reset_index()
winning_stats.columns = ['单位类型', '项目数量']

print("招标单位统计:")
print(bidding_stats)
print("\n中标单位统计:")
print(winning_stats)

# 创建图表
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# 招标单位柱状图
colors1 = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
bars1 = ax1.bar(bidding_stats['单位类型'], bidding_stats['项目数量'], 
                color=colors1[:len(bidding_stats)], alpha=0.8)

ax1.set_title('招标单位分类统计', fontsize=14, fontweight='bold', pad=20)
ax1.set_xlabel('单位类型', fontsize=12)
ax1.set_ylabel('项目数量', fontsize=12)
ax1.tick_params(axis='x', rotation=45)

# 添加数值标签
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11)

# 中标单位柱状图
colors2 = ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43']
bars2 = ax2.bar(winning_stats['单位类型'], winning_stats['项目数量'], 
                color=colors2[:len(winning_stats)], alpha=0.8)

ax2.set_title('中标单位分类统计', fontsize=14, fontweight='bold', pad=20)
ax2.set_xlabel('单位类型', fontsize=12)
ax2.set_ylabel('项目数量', fontsize=12)
ax2.tick_params(axis='x', rotation=45)

# 添加数值标签
for bar in bars2:
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11)

# 添加网格
ax1.grid(True, alpha=0.3, axis='y')
ax2.grid(True, alpha=0.3, axis='y')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('招投标单位分类统计图.png', dpi=300, bbox_inches='tight', 
            facecolor='white', edgecolor='none')

print("\n图表已保存为: 招投标单位分类统计图.png")

# 输出详细分析
print("\n" + "="*60)
print("招标单位详细分析:")
print("="*60)
total_bidding = bidding_stats['项目数量'].sum()
for _, row in bidding_stats.iterrows():
    percentage = (row['项目数量'] / total_bidding) * 100
    print(f"{row['单位类型']}: {row['项目数量']}个项目 ({percentage:.1f}%)")

print("\n" + "="*60)
print("中标单位详细分析:")
print("="*60)
total_winning = winning_stats['项目数量'].sum()
for _, row in winning_stats.iterrows():
    percentage = (row['项目数量'] / total_winning) * 100
    print(f"{row['单位类型']}: {row['项目数量']}个项目 ({percentage:.1f}%)")

# 保存分类结果到CSV
df_clean[['项目标题', '招标单位', '招标单位分类', '中标单位', '中标单位分类']].to_csv(
    '单位分类结果.csv', index=False, encoding='utf-8-sig')
print(f"\n分类结果已保存到: 单位分类结果.csv")
