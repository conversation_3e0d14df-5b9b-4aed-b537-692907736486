import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams
import re

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 处理金额数据
df['中标价格_清理'] = df['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df['中标价格_数值'] = pd.to_numeric(df['中标价格_清理'], errors='coerce')

# 提取城市信息
def extract_city(region):
    if pd.isna(region):
        return '其他'
    region = str(region)
    if '苏州' in region:
        return '苏州'
    elif '南京' in region:
        return '南京'
    elif '无锡' in region:
        return '无锡'
    else:
        return '其他'

df['城市'] = df['地区'].apply(extract_city)

# 筛选三个重点城市
target_cities = ['苏州', '南京', '无锡']
df_cities = df[df['城市'].isin(target_cities)].copy()

print("=" * 80)
print("苏州、南京、无锡低空经济项目分析报告")
print("=" * 80)

# 1. 市场规模对比
print("\n1. 市场规模对比")
print("-" * 50)
market_size = df_cities.groupby('城市').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
market_size.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
market_size = market_size.reset_index().sort_values('总金额(万元)', ascending=False)

print(market_size.to_string(index=False))

# 计算市场份额
total_projects = market_size['项目数量'].sum()
total_amount = market_size['总金额(万元)'].sum()
market_size['项目占比(%)'] = (market_size['项目数量'] / total_projects * 100).round(1)
market_size['金额占比(%)'] = (market_size['总金额(万元)'] / total_amount * 100).round(1)

print(f"\n市场份额分析:")
for _, row in market_size.iterrows():
    print(f"{row['城市']}: 项目{row['项目占比(%)']}%, 金额{row['金额占比(%)']}%")

# 2. 项目类型分布
print("\n\n2. 项目类型分布")
print("-" * 50)
procurement_by_city = pd.crosstab(df_cities['城市'], df_cities['采购类型'], margins=True)
print(procurement_by_city)

# 计算各城市项目类型占比
print(f"\n各城市项目类型占比:")
for city in target_cities:
    city_data = df_cities[df_cities['城市'] == city]
    type_dist = city_data['采购类型'].value_counts(normalize=True) * 100
    print(f"\n{city}:")
    for ptype, pct in type_dist.items():
        print(f"  {ptype}: {pct:.1f}%")

# 3. 招标主体分析
print("\n\n3. 招标主体分析")
print("-" * 50)

# 读取优化后的分类结果
df_classified = pd.read_csv('优化后单位分类结果.csv', encoding='utf-8')
df_classified['城市'] = df_classified['项目标题'].map(
    dict(zip(df['项目标题'], df['城市']))
)
df_cities_classified = df_classified[df_classified['城市'].isin(target_cities)]

bidding_by_city = pd.crosstab(df_cities_classified['城市'], 
                             df_cities_classified['招标单位分类'], 
                             margins=True)
print("招标单位类型分布:")
print(bidding_by_city)

# 4. 中标竞争格局
print("\n\n4. 中标竞争格局")
print("-" * 50)
winning_by_city = pd.crosstab(df_cities_classified['城市'], 
                             df_cities_classified['中标单位分类'], 
                             margins=True)
print("中标单位类型分布:")
print(winning_by_city)

# 5. 项目金额分布
print("\n\n5. 项目金额分布")
print("-" * 50)

def categorize_amount(amount):
    if pd.isna(amount):
        return '未知'
    if amount >= 1000:
        return '大型项目(≥1000万)'
    elif amount >= 100:
        return '中型项目(100-1000万)'
    else:
        return '小型项目(<100万)'

df_cities['项目规模'] = df_cities['中标价格_数值'].apply(categorize_amount)
amount_by_city = pd.crosstab(df_cities['城市'], df_cities['项目规模'], margins=True)
print(amount_by_city)

# 6. 时间趋势分析
print("\n\n6. 时间趋势分析")
print("-" * 50)

# 处理发布时间
df_cities['发布时间_处理'] = pd.to_datetime(df_cities['发布时间'], errors='coerce')
df_cities['发布月份'] = df_cities['发布时间_处理'].dt.to_period('M')

# 按月统计
monthly_trend = df_cities.groupby(['城市', '发布月份']).size().unstack(fill_value=0)
print("各城市月度项目发布趋势:")
print(monthly_trend.tail(6))  # 显示最近6个月

# 7. 细分领域机会分析
print("\n\n7. 细分领域机会分析")
print("-" * 50)

# 测绘相关项目识别
def is_surveying_related(title):
    if pd.isna(title):
        return False
    title = str(title)
    keywords = ['测绘', '勘察', '规划', '设计', '地理信息', '空域', '布局', '建模']
    return any(keyword in title for keyword in keywords)

df_cities['测绘相关'] = df_cities['项目标题'].apply(is_surveying_related)
surveying_projects = df_cities[df_cities['测绘相关'] == True]

print("测绘相关项目分布:")
surveying_by_city = surveying_projects.groupby('城市').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
surveying_by_city.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
print(surveying_by_city.to_string())

# 8. 主要参与企业分析
print("\n\n8. 主要参与企业分析")
print("-" * 50)

for city in target_cities:
    city_data = df_cities[df_cities['城市'] == city]
    print(f"\n{city}主要中标单位 (Top 5):")
    top_winners = city_data['中标单位'].value_counts().head(5)
    for company, count in top_winners.items():
        print(f"  {company}: {count}个项目")
    
    print(f"\n{city}主要招标单位 (Top 5):")
    top_bidders = city_data['招标单位'].value_counts().head(5)
    for company, count in top_bidders.items():
        print(f"  {company}: {count}个项目")

# 保存分析结果
market_size.to_csv('三城市市场规模对比.csv', index=False, encoding='utf-8-sig')
df_cities.to_csv('三城市详细数据.csv', index=False, encoding='utf-8-sig')
surveying_projects.to_csv('三城市测绘相关项目.csv', index=False, encoding='utf-8-sig')

print(f"\n\n分析结果已保存:")
print(f"1. 三城市市场规模对比.csv")
print(f"2. 三城市详细数据.csv") 
print(f"3. 三城市测绘相关项目.csv")
