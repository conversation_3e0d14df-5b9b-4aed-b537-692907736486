import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')
df_classified = pd.read_csv('单位分类结果.csv', encoding='utf-8')
df_original = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 处理金额数据
df['中标价格_清理'] = df['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df['中标价格_数值'] = pd.to_numeric(df['中标价格_清理'], errors='coerce')
df_valid = df[df['中标价格_数值'].notna() & (df['中标价格_数值'] > 0)].copy()

# 处理原始数据金额
df_original['中标价格_清理'] = df_original['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df_original['中标价格_数值'] = pd.to_numeric(df_original['中标价格_清理'], errors='coerce')

# 合并分类数据
df_merged = df_classified.merge(df_original[['项目标题', '中标价格_数值']], on='项目标题', how='left')

# 1. 采购类型统计图
procurement_stats = df_valid.groupby('采购类型').agg({
    '中标价格_数值': ['sum', 'count']
}).round(2)
procurement_stats.columns = ['总金额(万元)', '项目个数']
procurement_stats = procurement_stats.reset_index()

fig1, ax1 = plt.subplots(figsize=(12, 8))
ax2 = ax1.twinx()

x = np.arange(len(procurement_stats))
width = 0.35

bars1 = ax1.bar(x - width/2, procurement_stats['总金额(万元)'], width, 
               label='金额(万元)', color='#2E86AB', alpha=0.8)
bars2 = ax2.bar(x + width/2, procurement_stats['项目个数'], width, 
                label='项目个数', color='#A23B72', alpha=0.8)

ax1.set_xlabel('采购类型', fontsize=12, fontweight='bold')
ax1.set_xticks(x)
ax1.set_xticklabels(procurement_stats['采购类型'], fontsize=11)
ax1.set_ylabel('金额(万元)', fontsize=12, fontweight='bold', color='#2E86AB')
ax2.set_ylabel('项目个数', fontsize=12, fontweight='bold', color='#A23B72')
ax1.tick_params(axis='y', labelcolor='#2E86AB')
ax2.tick_params(axis='y', labelcolor='#A23B72')

for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
    height1 = bar1.get_height()
    ax1.text(bar1.get_x() + bar1.get_width()/2., height1 + height1*0.01,
            f'{height1:.1f}万', ha='center', va='bottom', fontsize=10, color='#2E86AB')
    height2 = bar2.get_height()
    ax2.text(bar2.get_x() + bar2.get_width()/2., height2 + height2*0.01,
             f'{int(height2)}个', ha='center', va='bottom', fontsize=10, color='#A23B72')

plt.title('近一年江苏低空中标信息统计\n按采购类型分类', fontsize=16, fontweight='bold', pad=20)
lines1, labels1 = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=11)
ax1.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('1_采购类型统计图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 2. 招标单位项目数量统计图
bidding_stats = df_classified['招标单位分类'].value_counts().reset_index()
bidding_stats.columns = ['单位类型', '项目数量']

fig2, ax = plt.subplots(figsize=(10, 8))
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
bars = ax.bar(bidding_stats['单位类型'], bidding_stats['项目数量'], 
              color=colors[:len(bidding_stats)], alpha=0.8)

ax.set_title('招标单位分类统计', fontsize=16, fontweight='bold', pad=20)
ax.set_xlabel('单位类型', fontsize=12, fontweight='bold')
ax.set_ylabel('项目数量', fontsize=12, fontweight='bold')
ax.tick_params(axis='x', rotation=45)

for bar in bars:
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('2_招标单位统计图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 3. 中标单位项目数量统计图
winning_stats = df_classified['中标单位分类'].value_counts().reset_index()
winning_stats.columns = ['单位类型', '项目数量']

fig3, ax = plt.subplots(figsize=(10, 8))
colors = ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43']
bars = ax.bar(winning_stats['单位类型'], winning_stats['项目数量'], 
              color=colors[:len(winning_stats)], alpha=0.8)

ax.set_title('中标单位分类统计', fontsize=16, fontweight='bold', pad=20)
ax.set_xlabel('单位类型', fontsize=12, fontweight='bold')
ax.set_ylabel('项目数量', fontsize=12, fontweight='bold')
ax.tick_params(axis='x', rotation=45)

for bar in bars:
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax.grid(True, alpha=0.3, axis='y')
plt.tight_layout()
plt.savefig('3_中标单位统计图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 4. 招标单位项目数量饼图
bidding_table = df_merged.groupby('招标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
bidding_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
bidding_table = bidding_table.reset_index().sort_values('项目数量', ascending=False)

fig4, ax = plt.subplots(figsize=(10, 8))
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
wedges, texts, autotexts = ax.pie(bidding_table['项目数量'], 
                                  labels=bidding_table['招标单位分类'],
                                  colors=colors[:len(bidding_table)],
                                  autopct='%1.1f%%',
                                  startangle=90,
                                  textprops={'fontsize': 12})

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax.set_title('招标单位项目数量分布', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()
plt.savefig('4_招标单位项目数量饼图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 5. 招标单位金额分布饼图
fig5, ax = plt.subplots(figsize=(10, 8))
wedges, texts, autotexts = ax.pie(bidding_table['总金额(万元)'], 
                                  labels=bidding_table['招标单位分类'],
                                  colors=colors[:len(bidding_table)],
                                  autopct='%1.1f%%',
                                  startangle=90,
                                  textprops={'fontsize': 12})

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax.set_title('招标单位金额分布', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()
plt.savefig('5_招标单位金额分布饼图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 6. 中标单位项目数量饼图
winning_table = df_merged.groupby('中标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
winning_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
winning_table = winning_table.reset_index().sort_values('项目数量', ascending=False)

fig6, ax = plt.subplots(figsize=(10, 8))
colors2 = ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43']
wedges, texts, autotexts = ax.pie(winning_table['项目数量'],
                                  labels=winning_table['中标单位分类'],
                                  colors=colors2[:len(winning_table)],
                                  autopct='%1.1f%%',
                                  startangle=90,
                                  textprops={'fontsize': 12})

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax.set_title('中标单位项目数量分布', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()
plt.savefig('6_中标单位项目数量饼图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 7. 中标单位金额分布饼图
fig7, ax = plt.subplots(figsize=(10, 8))
wedges, texts, autotexts = ax.pie(winning_table['总金额(万元)'],
                                  labels=winning_table['中标单位分类'],
                                  colors=colors2[:len(winning_table)],
                                  autopct='%1.1f%%',
                                  startangle=90,
                                  textprops={'fontsize': 12})

for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax.set_title('中标单位金额分布', fontsize=16, fontweight='bold', pad=20)
plt.tight_layout()
plt.savefig('7_中标单位金额分布饼图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

print("所有单独图表已生成完成！")
print("生成的图表文件：")
print("1. 1_采购类型统计图.png")
print("2. 2_招标单位统计图.png")
print("3. 3_中标单位统计图.png")
print("4. 4_招标单位项目数量饼图.png")
print("5. 5_招标单位金额分布饼图.png")
print("6. 6_中标单位项目数量饼图.png")
print("7. 7_中标单位金额分布饼图.png")
