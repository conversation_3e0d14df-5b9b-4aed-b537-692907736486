import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取分析结果
market_data = pd.read_csv('三城市市场规模对比.csv', encoding='utf-8')
cities_data = pd.read_csv('三城市详细数据.csv', encoding='utf-8')
surveying_data = pd.read_csv('三城市测绘相关项目.csv', encoding='utf-8')

# 1. 市场规模对比图
fig1, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# 项目数量对比
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
bars1 = ax1.bar(market_data['城市'], market_data['项目数量'], color=colors, alpha=0.8)
ax1.set_title('三城市项目数量对比', fontsize=14, fontweight='bold')
ax1.set_ylabel('项目数量', fontsize=12)

for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=12, fontweight='bold')

# 总金额对比
bars2 = ax2.bar(market_data['城市'], market_data['总金额(万元)'], color=colors, alpha=0.8)
ax2.set_title('三城市总金额对比', fontsize=14, fontweight='bold')
ax2.set_ylabel('总金额(万元)', fontsize=12)

for bar in bars2:
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{height:.0f}万', ha='center', va='bottom', fontsize=12, fontweight='bold')

plt.suptitle('苏州、南京、无锡低空经济市场规模对比', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('三城市市场规模对比图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.close()

# 2. 项目类型分布图
fig2, axes = plt.subplots(1, 3, figsize=(18, 6))
target_cities = ['苏州', '南京', '无锡']
colors_type = ['#FF9FF3', '#54A0FF', '#5F27CD']

for i, city in enumerate(target_cities):
    city_data = cities_data[cities_data['城市'] == city]
    type_counts = city_data['采购类型'].value_counts()
    
    wedges, texts, autotexts = axes[i].pie(type_counts.values, 
                                          labels=type_counts.index,
                                          colors=colors_type,
                                          autopct='%1.1f%%',
                                          startangle=90)
    
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
    
    axes[i].set_title(f'{city}项目类型分布', fontsize=14, fontweight='bold')

plt.suptitle('三城市项目类型分布对比', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('三城市项目类型分布图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.close()

# 3. 测绘相关项目分析图
surveying_summary = surveying_data.groupby('城市').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
surveying_summary.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
surveying_summary = surveying_summary.reset_index()

fig3, (ax3, ax4) = plt.subplots(1, 2, figsize=(16, 8))

# 测绘项目数量
bars3 = ax3.bar(surveying_summary['城市'], surveying_summary['项目数量'], 
                color=['#26de81', '#fd79a8', '#fdcb6e'], alpha=0.8)
ax3.set_title('测绘相关项目数量', fontsize=14, fontweight='bold')
ax3.set_ylabel('项目数量', fontsize=12)

for bar in bars3:
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=12, fontweight='bold')

# 测绘项目平均金额
bars4 = ax4.bar(surveying_summary['城市'], surveying_summary['平均金额(万元)'], 
                color=['#26de81', '#fd79a8', '#fdcb6e'], alpha=0.8)
ax4.set_title('测绘相关项目平均金额', fontsize=14, fontweight='bold')
ax4.set_ylabel('平均金额(万元)', fontsize=12)

for bar in bars4:
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{height:.0f}万', ha='center', va='bottom', fontsize=12, fontweight='bold')

plt.suptitle('三城市测绘相关项目分析', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('三城市测绘项目分析图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.close()

# 4. 项目规模分布图
fig4, axes = plt.subplots(1, 3, figsize=(18, 6))

# 定义项目规模分类
def categorize_amount(amount):
    if pd.isna(amount):
        return '未知'
    if amount >= 1000:
        return '大型项目(≥1000万)'
    elif amount >= 100:
        return '中型项目(100-1000万)'
    else:
        return '小型项目(<100万)'

cities_data['项目规模'] = cities_data['中标价格_数值'].apply(categorize_amount)
colors_scale = ['#e17055', '#fdcb6e', '#00b894']

for i, city in enumerate(target_cities):
    city_data = cities_data[cities_data['城市'] == city]
    scale_counts = city_data['项目规模'].value_counts()
    
    # 确保顺序一致
    scale_order = ['小型项目(<100万)', '中型项目(100-1000万)', '大型项目(≥1000万)']
    scale_counts = scale_counts.reindex(scale_order, fill_value=0)
    
    wedges, texts, autotexts = axes[i].pie(scale_counts.values, 
                                          labels=scale_counts.index,
                                          colors=colors_scale,
                                          autopct='%1.1f%%',
                                          startangle=90)
    
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(10)
    
    axes[i].set_title(f'{city}项目规模分布', fontsize=14, fontweight='bold')

plt.suptitle('三城市项目规模分布对比', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('三城市项目规模分布图.png', dpi=300, bbox_inches='tight', facecolor='white')
plt.close()

print("所有图表已生成完成！")
print("生成的图表文件：")
print("1. 三城市市场规模对比图.png")
print("2. 三城市项目类型分布图.png")
print("3. 三城市测绘项目分析图.png")
print("4. 三城市项目规模分布图.png")

# 输出关键洞察
print("\n" + "=" * 80)
print("关键洞察总结")
print("=" * 80)

print("\n【市场规模洞察】")
print(f"• 苏州是绝对龙头：项目数量{market_data.iloc[0]['项目数量']}个，占比{market_data.iloc[0]['项目占比(%)']}%")
print(f"• 南京紧随其后：项目数量{market_data.iloc[1]['项目数量']}个，平均金额略高于苏州")
print(f"• 无锡市场较小：仅{market_data.iloc[2]['项目数量']}个项目，但发展潜力大")

print("\n【测绘机会洞察】")
print(f"• 苏州测绘项目最多：{surveying_summary.iloc[0]['项目数量']}个，平均金额{surveying_summary.iloc[0]['平均金额(万元)']}万")
print(f"• 南京项目数量适中：{surveying_summary.iloc[1]['项目数量']}个，竞争相对缓和")
print(f"• 无锡虽然项目少但平均金额不低：{surveying_summary.iloc[2]['平均金额(万元)']}万/个")

print("\n【竞争格局洞察】")
print("• 苏州：本地企业活跃，江苏特零、苏州低空科技等占据优势")
print("• 南京：华设设计等大型设计院主导，技术门槛较高")
print("• 无锡：外地企业参与较多，本地化机会较大")
