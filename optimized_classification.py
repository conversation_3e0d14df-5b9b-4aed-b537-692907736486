import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取CSV文件
df = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 清理数据，去除空值
df_clean = df.dropna(subset=['中标单位', '招标单位'])

def classify_bidding_unit_optimized(unit_name):
    """优化后的招标单位分类"""
    if pd.isna(unit_name):
        return '其他'
    
    unit_name = str(unit_name).strip()
    
    # 政府单位关键词
    gov_keywords = ['政府', '管委会', '管理委员会', '发改委', '交通局', '公安局', '气象局', 
                   '自然资源', '规划局', '街道', '镇政府', '区政府', '市政府', '县政府',
                   '发展和改革', '交通运输', '生态环境', '应急管理', '城管', '综合执法',
                   '人力资源和社会保障', '统一战线工作部', '邮政业安全发展中心']
    
    # 国企关键词
    soe_keywords = ['集团', '建设', '投资', '发展', '控股', '置业', '产业园', '开发区',
                   '城投', '交投', '国投', '建投', '文旅', '科技城', '新城', '园区',
                   '高新投资', '产业投资', '开发控股', '城发', '通达投资']
    
    # 三大运营商
    telecom_keywords = ['中国电信', '中国移动', '中国联通', '电信', '移动', '联通']
    
    # 高校关键词
    university_keywords = ['大学', '学院', '职业技术', '专业学校', '教育', '财经高等职业']
    
    # 低空经济专业公司
    lowalt_keywords = ['低空', '无人机', '航空', '飞行服务', '空域', '通用机场']
    
    # 科技公司
    tech_keywords = ['科技', '信息技术', '智能', '数字', '网络', '软件', '电子']
    
    # 检查三大运营商
    for keyword in telecom_keywords:
        if keyword in unit_name:
            return '三大运营商'
    
    # 检查政府
    for keyword in gov_keywords:
        if keyword in unit_name:
            return '政府'
    
    # 检查高校
    for keyword in university_keywords:
        if keyword in unit_name:
            return '高校'
    
    # 检查低空经济专业公司
    for keyword in lowalt_keywords:
        if keyword in unit_name:
            return '低空经济公司'
    
    # 检查国企
    for keyword in soe_keywords:
        if keyword in unit_name:
            return '国企'
    
    # 检查科技公司
    for keyword in tech_keywords:
        if keyword in unit_name:
            return '科技公司'
    
    return '其他'

def classify_winning_unit_optimized(unit_name):
    """优化后的中标单位分类"""
    if pd.isna(unit_name):
        return '其他'
    
    unit_name = str(unit_name).strip()
    
    # 国企关键词
    soe_keywords = ['集团', '建设', '工程', '建筑', '设计院', '咨询', '科工', '中建', 
                   '中交', '中铁', '中冶', '航天', '中航', '中电', '国电', '华能',
                   '中通服', '中邮建', '苏交科', '华设', '江苏', '建龙', '嘉盛',
                   '国家发展和改革委员会', '中国民航科学技术研究院']
    
    # 三大运营商
    telecom_keywords = ['中国电信', '中国移动', '中国联通', '电信', '移动', '联通']
    
    # 高校关键词
    university_keywords = ['大学', '学院', '航空航天', '科技大学']
    
    # 测绘单位关键词
    survey_keywords = ['测绘', '地理信息', '勘察', '规划设计', '建筑设计', '市政', '勘测']
    
    # 低空经济专业公司
    lowalt_keywords = ['低空', '无人机', '航空', '飞行', '空域', '通用机场', '亿航']
    
    # 科技公司
    tech_keywords = ['科技', '信息技术', '智能', '数字', '网络', '软件', '电子', '传媒']
    
    # 检查三大运营商
    for keyword in telecom_keywords:
        if keyword in unit_name:
            return '三大运营商'
    
    # 检查高校
    for keyword in university_keywords:
        if keyword in unit_name:
            return '高校'
    
    # 检查测绘单位
    for keyword in survey_keywords:
        if keyword in unit_name:
            return '测绘单位'
    
    # 检查低空经济专业公司
    for keyword in lowalt_keywords:
        if keyword in unit_name:
            return '低空经济公司'
    
    # 检查国企
    for keyword in soe_keywords:
        if keyword in unit_name:
            return '国企'
    
    # 检查科技公司
    for keyword in tech_keywords:
        if keyword in unit_name:
            return '科技公司'
    
    return '其他'

# 应用优化后的分类函数
df_clean['招标单位分类'] = df_clean['招标单位'].apply(classify_bidding_unit_optimized)
df_clean['中标单位分类'] = df_clean['中标单位'].apply(classify_winning_unit_optimized)

# 统计招标单位
bidding_stats = df_clean['招标单位分类'].value_counts().reset_index()
bidding_stats.columns = ['单位类型', '项目数量']

# 统计中标单位
winning_stats = df_clean['中标单位分类'].value_counts().reset_index()
winning_stats.columns = ['单位类型', '项目数量']

print("优化后的招标单位统计:")
print(bidding_stats)
print("\n优化后的中标单位统计:")
print(winning_stats)

# 处理金额数据
df['中标价格_清理'] = df['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df['中标价格_数值'] = pd.to_numeric(df['中标价格_清理'], errors='coerce')

# 合并数据获取金额信息
df_merged = df_clean.merge(df[['项目标题', '中标价格_数值']], on='项目标题', how='left')

# 创建招标单位详细统计表
bidding_table = df_merged.groupby('招标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
bidding_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
bidding_table = bidding_table.reset_index().sort_values('项目数量', ascending=False)

# 创建中标单位详细统计表
winning_table = df_merged.groupby('中标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
winning_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
winning_table = winning_table.reset_index().sort_values('项目数量', ascending=False)

print("\n招标单位详细统计:")
print(bidding_table)
print("\n中标单位详细统计:")
print(winning_table)

# 保存优化后的分类结果
df_clean[['项目标题', '招标单位', '招标单位分类', '中标单位', '中标单位分类']].to_csv(
    '优化后单位分类结果.csv', index=False, encoding='utf-8-sig')

print(f"\n优化后的分类结果已保存到: 优化后单位分类结果.csv")
