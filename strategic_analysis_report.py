import pandas as pd

# 读取数据
cities_data = pd.read_csv('三城市详细数据.csv', encoding='utf-8')
surveying_data = pd.read_csv('三城市测绘相关项目.csv', encoding='utf-8')
market_data = pd.read_csv('三城市市场规模对比.csv', encoding='utf-8')

print("=" * 100)
print("苏州园区测绘公司低空经济市场切入战略分析报告")
print("=" * 100)

print("\n📊 一、三城市市场对比分析")
print("-" * 80)

print("\n【市场规模排名】")
for i, row in market_data.iterrows():
    print(f"{i+1}. {row['城市']}: {row['项目数量']}个项目, {row['总金额(万元)']}万元, 平均{row['平均金额(万元)']}万元/个")

print("\n【测绘项目机会分析】")
surveying_summary = surveying_data.groupby('城市').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
surveying_summary.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
surveying_summary = surveying_summary.reset_index()

for _, row in surveying_summary.iterrows():
    print(f"• {row['城市']}: {row['项目数量']}个测绘项目, 总金额{row['总金额(万元)']}万, 平均{row['平均金额(万元)']}万/个")

print("\n🎯 二、各城市切入策略分析")
print("-" * 80)

print("\n【苏州市场 - 首选目标】⭐⭐⭐⭐⭐")
suzhou_data = cities_data[cities_data['城市'] == '苏州']
suzhou_surveying = surveying_data[surveying_data['城市'] == '苏州']

print(f"✅ 优势:")
print(f"  • 市场规模最大: 85个项目，占三城市总量54.1%")
print(f"  • 地理优势明显: 园区测绘位于苏州，就近服务成本低")
print(f"  • 测绘项目相对较多: 7个相关项目")
print(f"  • 本地企业活跃: 苏州低空科技、江苏特零等本地化程度高")

print(f"\n⚠️ 挑战:")
print(f"  • 竞争激烈: 本地企业众多，市场相对成熟")
print(f"  • 平均项目金额适中: 638万元，不算最高")

print(f"\n🚀 切入建议:")
print(f"  • 重点关注起降点规划、空域建模等专业领域")
print(f"  • 与苏州低空科技、苏州新索慧知等建立合作关系")
print(f"  • 主攻政府和国企项目，这两类占比较高")

print("\n【南京市场 - 重点拓展】⭐⭐⭐⭐")
nanjing_data = cities_data[cities_data['城市'] == '南京']
nanjing_surveying = surveying_data[surveying_data['城市'] == '南京']

print(f"✅ 优势:")
print(f"  • 项目质量高: 平均金额653万元，略高于苏州")
print(f"  • 政策支持强: 省会城市，政府项目较多")
print(f"  • 技术要求高: 华设设计等大院主导，技术门槛高但利润丰厚")

print(f"\n⚠️ 挑战:")
print(f"  • 竞争对手实力强: 华设设计、苏交科等大型设计院占主导")
print(f"  • 距离相对较远: 需要建立本地化服务能力")

print(f"\n🚀 切入建议:")
print(f"  • 与华设设计、苏交科等建立分包合作关系")
print(f"  • 重点关注省级、市级重大规划项目")
print(f"  • 提升技术能力，满足高端项目需求")

print("\n【无锡市场 - 潜力挖掘】⭐⭐⭐")
wuxi_data = cities_data[cities_data['城市'] == '无锡']
wuxi_surveying = surveying_data[surveying_data['城市'] == '无锡']

print(f"✅ 优势:")
print(f"  • 竞争相对缓和: 项目数量少，外地企业参与较多")
print(f"  • 测绘项目价值高: 平均152万元，性价比不错")
print(f"  • 发展潜力大: 市场处于起步阶段，机会较多")

print(f"\n⚠️ 挑战:")
print(f"  • 市场规模小: 仅17个项目，总量有限")
print(f"  • 项目类型相对单一: 主要集中在规划设计")

print(f"\n🚀 切入建议:")
print(f"  • 抢占先机，建立本地化优势")
print(f"  • 重点关注丁蜀通用机场等重点项目")
print(f"  • 与深圳城市交通规划设计中心等外地企业竞争")

print("\n💡 三、具体项目机会分析")
print("-" * 80)

print("\n【高价值测绘项目类型】")
high_value_projects = surveying_data[surveying_data['中标价格_数值'] >= 200]
print(f"200万以上项目共{len(high_value_projects)}个:")
for _, project in high_value_projects.iterrows():
    print(f"  • {project['项目标题'][:50]}... ({project['中标价格_数值']}万元, {project['城市']})")

print("\n【典型项目类型分析】")
project_types = {}
for _, project in surveying_data.iterrows():
    title = project['项目标题']
    if '规划' in title:
        project_types.setdefault('规划设计', []).append(project)
    elif '设计' in title:
        project_types.setdefault('工程设计', []).append(project)
    elif '空域' in title or '航线' in title:
        project_types.setdefault('空域规划', []).append(project)
    elif '基础设施' in title:
        project_types.setdefault('基础设施', []).append(project)
    else:
        project_types.setdefault('其他', []).append(project)

for ptype, projects in project_types.items():
    avg_amount = sum(p['中标价格_数值'] for p in projects) / len(projects)
    print(f"• {ptype}: {len(projects)}个项目, 平均{avg_amount:.1f}万元")

print("\n🎯 四、行动计划建议")
print("-" * 80)

print("\n【短期行动(1-3个月)】")
print("1. 市场调研深化:")
print("   • 实地走访苏州主要低空经济企业")
print("   • 建立与苏州低空科技、新索慧知等企业的联系")
print("   • 了解华设设计、苏交科的合作模式")

print("\n2. 能力建设:")
print("   • 申请低空经济相关测绘资质")
print("   • 培训团队掌握空域建模、航线规划等专业技能")
print("   • 开发标准化的低空测绘产品和服务")

print("\n【中期行动(3-12个月)】")
print("1. 合作伙伴建立:")
print("   • 与苏州本地科技公司建立战略合作")
print("   • 与华设设计等大院建立分包关系")
print("   • 加入相关行业协会和产业联盟")

print("\n2. 项目实施:")
print("   • 重点投标苏州地区50-300万元规模项目")
print("   • 以分包方式参与南京大型项目")
print("   • 抢占无锡市场先机")

print("\n【长期规划(1-3年)】")
print("1. 市场地位确立:")
print("   • 成为苏州地区低空测绘领域知名企业")
print("   • 在三城市建立稳定的业务网络")
print("   • 年营收目标: 2000-3000万元")

print("\n2. 业务拓展:")
print("   • 向长三角其他城市扩展")
print("   • 开发低空经济全产业链服务能力")
print("   • 考虑设立南京、无锡分支机构")

print("\n📈 五、预期收益分析")
print("-" * 80)
print("基于市场分析，预期收益如下:")
print("• 第一年: 获得3-5个项目，营收500-800万元")
print("• 第二年: 获得6-10个项目，营收1200-2000万元") 
print("• 第三年: 获得10-15个项目，营收2000-3000万元")
print("• 市场份额目标: 在三城市测绘相关项目中占比15-20%")

print("\n" + "=" * 100)
print("报告完成 - 建议优先布局苏州，重点拓展南京，适时进入无锡")
print("=" * 100)
