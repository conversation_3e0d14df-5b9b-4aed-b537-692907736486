import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取CSV文件
df = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')

# 清理数据
# 处理中标价格列，去除非数字字符并转换为数值
df['中标价格_清理'] = df['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df['中标价格_数值'] = pd.to_numeric(df['中标价格_清理'], errors='coerce')

# 过滤掉价格为空或0的记录
df_valid = df[df['中标价格_数值'].notna() & (df['中标价格_数值'] > 0)].copy()

# 统计各采购类型的数据
procurement_stats = df_valid.groupby('采购类型').agg({
    '中标价格_数值': ['sum', 'count']
}).round(2)

# 重命名列
procurement_stats.columns = ['总金额(万元)', '项目个数']
procurement_stats = procurement_stats.reset_index()

print("采购类型统计:")
print(procurement_stats)

# 创建图表
fig, ax = plt.subplots(figsize=(12, 8))

# 设置柱子的位置
x = np.arange(len(procurement_stats))
width = 0.35

# 创建双y轴
ax2 = ax.twinx()

# 绘制金额柱状图（左侧y轴）
bars1 = ax.bar(x - width/2, procurement_stats['总金额(万元)'], width, 
               label='金额(万元)', color='#2E86AB', alpha=0.8)

# 绘制项目个数柱状图（右侧y轴）
bars2 = ax2.bar(x + width/2, procurement_stats['项目个数'], width, 
                label='项目个数', color='#A23B72', alpha=0.8)

# 设置x轴标签
ax.set_xlabel('采购类型', fontsize=12, fontweight='bold')
ax.set_xticks(x)
ax.set_xticklabels(procurement_stats['采购类型'], fontsize=11)

# 设置y轴标签
ax.set_ylabel('金额(万元)', fontsize=12, fontweight='bold', color='#2E86AB')
ax2.set_ylabel('项目个数', fontsize=12, fontweight='bold', color='#A23B72')

# 设置y轴颜色
ax.tick_params(axis='y', labelcolor='#2E86AB')
ax2.tick_params(axis='y', labelcolor='#A23B72')

# 添加数值标签
for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
    # 金额标签
    height1 = bar1.get_height()
    ax.text(bar1.get_x() + bar1.get_width()/2., height1 + height1*0.01,
            f'{height1:.1f}万', ha='center', va='bottom', fontsize=10, color='#2E86AB')
    
    # 项目个数标签
    height2 = bar2.get_height()
    ax2.text(bar2.get_x() + bar2.get_width()/2., height2 + height2*0.01,
             f'{int(height2)}个', ha='center', va='bottom', fontsize=10, color='#A23B72')

# 设置标题
plt.title('近一年江苏低空中标信息统计\n按采购类型分类', fontsize=16, fontweight='bold', pad=20)

# 添加图例
lines1, labels1 = ax.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=11)

# 添加网格
ax.grid(True, alpha=0.3, axis='y')

# 调整布局
plt.tight_layout()

# 保存图表
plt.savefig('江苏低空中标信息统计图.png', dpi=300, bbox_inches='tight',
            facecolor='white', edgecolor='none')

print("图表已保存为: 江苏低空中标信息统计图.png")

# 输出详细统计信息
print("\n详细统计信息:")
print("="*50)
for _, row in procurement_stats.iterrows():
    print(f"{row['采购类型']}:")
    print(f"  总金额: {row['总金额(万元)']:.1f} 万元")
    print(f"  项目个数: {int(row['项目个数'])} 个")
    print(f"  平均金额: {row['总金额(万元)']/row['项目个数']:.1f} 万元/个")
    print("-" * 30)

print(f"\n总计:")
print(f"  总金额: {procurement_stats['总金额(万元)'].sum():.1f} 万元")
print(f"  总项目数: {int(procurement_stats['项目个数'].sum())} 个")
print(f"  整体平均金额: {procurement_stats['总金额(万元)'].sum()/procurement_stats['项目个数'].sum():.1f} 万元/个")
