import pandas as pd

# 读取分类结果
df = pd.read_csv('单位分类结果.csv', encoding='utf-8')

print("=== 招标单位中的'其他'类分析 ===")
bidding_others = df[df['招标单位分类'] == '其他']['招标单位'].value_counts()
print("招标单位'其他'类包含的单位：")
for unit, count in bidding_others.head(20).items():
    print(f"{unit}: {count}个项目")

print("\n=== 中标单位中的'其他'类分析 ===")
winning_others = df[df['中标单位分类'] == '其他']['中标单位'].value_counts()
print("中标单位'其他'类包含的单位：")
for unit, count in winning_others.head(20).items():
    print(f"{unit}: {count}个项目")

# 分析所有招标单位
print("\n=== 所有招标单位分析 ===")
all_bidding = df['招标单位'].value_counts()
print("出现频次最高的招标单位：")
for unit, count in all_bidding.head(15).items():
    print(f"{unit}: {count}个项目")

# 分析所有中标单位
print("\n=== 所有中标单位分析 ===")
all_winning = df['中标单位'].value_counts()
print("出现频次最高的中标单位：")
for unit, count in all_winning.head(15).items():
    print(f"{unit}: {count}个项目")
