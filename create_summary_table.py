import pandas as pd

# 读取数据
cities_data = pd.read_csv('三城市详细数据.csv', encoding='utf-8')
surveying_data = pd.read_csv('三城市测绘相关项目.csv', encoding='utf-8')
market_data = pd.read_csv('三城市市场规模对比.csv', encoding='utf-8')

# 创建综合分析表
summary_data = []

target_cities = ['苏州', '南京', '无锡']

for city in target_cities:
    # 基础市场数据
    city_market = market_data[market_data['城市'] == city].iloc[0]
    
    # 测绘项目数据
    city_surveying = surveying_data[surveying_data['城市'] == city]
    surveying_count = len(city_surveying)
    surveying_total = city_surveying['中标价格_数值'].sum() if len(city_surveying) > 0 else 0
    surveying_avg = city_surveying['中标价格_数值'].mean() if len(city_surveying) > 0 else 0
    
    # 项目类型分布
    city_projects = cities_data[cities_data['城市'] == city]
    service_pct = (city_projects['采购类型'] == '服务').sum() / len(city_projects) * 100
    goods_pct = (city_projects['采购类型'] == '货物').sum() / len(city_projects) * 100
    engineering_pct = (city_projects['采购类型'] == '工程').sum() / len(city_projects) * 100
    
    # 项目规模分布
    def categorize_amount(amount):
        if pd.isna(amount):
            return '未知'
        if amount >= 1000:
            return '大型项目'
        elif amount >= 100:
            return '中型项目'
        else:
            return '小型项目'
    
    city_projects['项目规模'] = city_projects['中标价格_数值'].apply(categorize_amount)
    large_pct = (city_projects['项目规模'] == '大型项目').sum() / len(city_projects) * 100
    medium_pct = (city_projects['项目规模'] == '中型项目').sum() / len(city_projects) * 100
    small_pct = (city_projects['项目规模'] == '小型项目').sum() / len(city_projects) * 100
    
    # 主要中标单位
    top_winners = city_projects['中标单位'].value_counts().head(3)
    top_winner_1 = f"{top_winners.index[0]}({top_winners.iloc[0]}个)" if len(top_winners) > 0 else ""
    top_winner_2 = f"{top_winners.index[1]}({top_winners.iloc[1]}个)" if len(top_winners) > 1 else ""
    top_winner_3 = f"{top_winners.index[2]}({top_winners.iloc[2]}个)" if len(top_winners) > 2 else ""
    
    # 主要招标单位
    top_bidders = city_projects['招标单位'].value_counts().head(3)
    top_bidder_1 = f"{top_bidders.index[0]}({top_bidders.iloc[0]}个)" if len(top_bidders) > 0 else ""
    top_bidder_2 = f"{top_bidders.index[1]}({top_bidders.iloc[1]}个)" if len(top_bidders) > 1 else ""
    top_bidder_3 = f"{top_bidders.index[2]}({top_bidders.iloc[2]}个)" if len(top_bidders) > 2 else ""
    
    # 竞争优势评估
    if city == '苏州':
        advantages = "地理优势明显;市场规模最大;本地企业活跃"
        challenges = "竞争激烈;市场相对成熟"
        strategy = "重点关注起降点规划;与本地企业合作;主攻政府国企项目"
        priority = "⭐⭐⭐⭐⭐"
        recommendation = "首选目标市场"
    elif city == '南京':
        advantages = "项目质量高;政策支持强;技术要求高利润丰厚"
        challenges = "竞争对手实力强;距离相对较远"
        strategy = "与大院建立分包关系;关注省市级项目;提升技术能力"
        priority = "⭐⭐⭐⭐"
        recommendation = "重点拓展市场"
    else:  # 无锡
        advantages = "竞争相对缓和;发展潜力大;外地企业参与多"
        challenges = "市场规模小;项目类型相对单一"
        strategy = "抢占先机;建立本地化优势;关注重点项目"
        priority = "⭐⭐⭐"
        recommendation = "潜力挖掘市场"
    
    # 汇总数据
    row_data = {
        '城市': city,
        '市场排名': list(target_cities).index(city) + 1,
        '总项目数': int(city_market['项目数量']),
        '总金额(万元)': city_market['总金额(万元)'],
        '平均金额(万元)': round(city_market['平均金额(万元)'], 1),
        '项目占比(%)': city_market['项目占比(%)'],
        '金额占比(%)': city_market['金额占比(%)'],
        '测绘项目数': surveying_count,
        '测绘总金额(万元)': round(surveying_total, 1),
        '测绘平均金额(万元)': round(surveying_avg, 1),
        '服务类占比(%)': round(service_pct, 1),
        '货物类占比(%)': round(goods_pct, 1),
        '工程类占比(%)': round(engineering_pct, 1),
        '大型项目占比(%)': round(large_pct, 1),
        '中型项目占比(%)': round(medium_pct, 1),
        '小型项目占比(%)': round(small_pct, 1),
        '主要中标单位1': top_winner_1,
        '主要中标单位2': top_winner_2,
        '主要中标单位3': top_winner_3,
        '主要招标单位1': top_bidder_1,
        '主要招标单位2': top_bidder_2,
        '主要招标单位3': top_bidder_3,
        '竞争优势': advantages,
        '面临挑战': challenges,
        '切入策略': strategy,
        '优先级': priority,
        '市场定位': recommendation
    }
    
    summary_data.append(row_data)

# 创建DataFrame
summary_df = pd.DataFrame(summary_data)

# 保存为CSV
summary_df.to_csv('苏州南京无锡低空经济市场综合分析表.csv', index=False, encoding='utf-8-sig')

print("综合分析表已生成：苏州南京无锡低空经济市场综合分析表.csv")
print("\n表格包含以下维度：")
print("• 市场规模数据（项目数、金额、占比）")
print("• 测绘项目专项分析")
print("• 项目类型和规模分布")
print("• 主要参与企业")
print("• 竞争分析和切入策略")
print("• 优先级和市场定位建议")

# 显示表格内容
print("\n" + "="*100)
print("综合分析表预览：")
print("="*100)

# 显示关键列
key_columns = ['城市', '市场排名', '总项目数', '总金额(万元)', '测绘项目数', 
               '测绘平均金额(万元)', '优先级', '市场定位']
print(summary_df[key_columns].to_string(index=False))

print(f"\n完整表格共{len(summary_df.columns)}列，已保存到CSV文件中。")
