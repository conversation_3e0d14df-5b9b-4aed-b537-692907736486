import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
rcParams['axes.unicode_minus'] = False

# 读取优化后的分类结果
df = pd.read_csv('优化后单位分类结果.csv', encoding='utf-8')

# 读取原始数据获取金额信息
df_original = pd.read_csv('近一年江苏低空中标信息.csv', encoding='utf-8')
df_original['中标价格_清理'] = df_original['中标价格'].astype(str).str.replace(',', '').str.replace('万', '').str.replace('元', '')
df_original['中标价格_数值'] = pd.to_numeric(df_original['中标价格_清理'], errors='coerce')

# 合并数据
df_merged = df.merge(df_original[['项目标题', '中标价格_数值']], on='项目标题', how='left')

# 创建招标单位统计表
bidding_table = df_merged.groupby('招标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
bidding_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
bidding_table = bidding_table.reset_index().sort_values('项目数量', ascending=False)

# 创建中标单位统计表
winning_table = df_merged.groupby('中标单位分类').agg({
    '项目标题': 'count',
    '中标价格_数值': ['sum', 'mean']
}).round(2)
winning_table.columns = ['项目数量', '总金额(万元)', '平均金额(万元)']
winning_table = winning_table.reset_index().sort_values('项目数量', ascending=False)

# 1. 招标单位合并图表（项目数量 + 金额分布）
fig1, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

# 招标单位项目数量柱状图
colors1 = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98FB98']
bars1 = ax1.bar(bidding_table['招标单位分类'], bidding_table['项目数量'], 
                color=colors1[:len(bidding_table)], alpha=0.8)

ax1.set_title('招标单位项目数量统计', fontsize=14, fontweight='bold')
ax1.set_xlabel('单位类型', fontsize=12)
ax1.set_ylabel('项目数量', fontsize=12)
ax1.tick_params(axis='x', rotation=45)

# 添加数值标签
for bar in bars1:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax1.grid(True, alpha=0.3, axis='y')

# 招标单位金额分布饼图
wedges1, texts1, autotexts1 = ax2.pie(bidding_table['总金额(万元)'], 
                                      labels=bidding_table['招标单位分类'],
                                      colors=colors1[:len(bidding_table)],
                                      autopct='%1.1f%%',
                                      startangle=90,
                                      textprops={'fontsize': 10})

for autotext in autotexts1:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax2.set_title('招标单位金额分布', fontsize=14, fontweight='bold')

plt.suptitle('招标单位分析', fontsize=16, fontweight='bold', y=0.98)
plt.tight_layout()
plt.savefig('招标单位综合分析图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 2. 中标单位合并图表（项目数量 + 金额分布）
fig2, (ax3, ax4) = plt.subplots(1, 2, figsize=(16, 8))

# 中标单位项目数量柱状图
colors2 = ['#FF9FF3', '#54A0FF', '#5F27CD', '#00D2D3', '#FF9F43', '#26de81', '#fd79a8']
bars2 = ax3.bar(winning_table['中标单位分类'], winning_table['项目数量'], 
                color=colors2[:len(winning_table)], alpha=0.8)

ax3.set_title('中标单位项目数量统计', fontsize=14, fontweight='bold')
ax3.set_xlabel('单位类型', fontsize=12)
ax3.set_ylabel('项目数量', fontsize=12)
ax3.tick_params(axis='x', rotation=45)

# 添加数值标签
for bar in bars2:
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
             f'{int(height)}', ha='center', va='bottom', fontsize=11, fontweight='bold')

ax3.grid(True, alpha=0.3, axis='y')

# 中标单位金额分布饼图
wedges2, texts2, autotexts2 = ax4.pie(winning_table['总金额(万元)'], 
                                      labels=winning_table['中标单位分类'],
                                      colors=colors2[:len(winning_table)],
                                      autopct='%1.1f%%',
                                      startangle=90,
                                      textprops={'fontsize': 10})

for autotext in autotexts2:
    autotext.set_color('white')
    autotext.set_fontweight('bold')

ax4.set_title('中标单位金额分布', fontsize=14, fontweight='bold')

plt.suptitle('中标单位分析', fontsize=16, fontweight='bold', y=0.98)
plt.tight_layout()
plt.savefig('中标单位综合分析图.png', dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
plt.close()

# 保存优化后的统计表
bidding_table['占比(%)'] = (bidding_table['项目数量'] / bidding_table['项目数量'].sum() * 100).round(1)
winning_table['占比(%)'] = (winning_table['项目数量'] / winning_table['项目数量'].sum() * 100).round(1)

bidding_table.to_csv('优化后招标单位统计表.csv', index=False, encoding='utf-8-sig')
winning_table.to_csv('优化后中标单位统计表.csv', index=False, encoding='utf-8-sig')

print("=" * 80)
print("优化后招标单位分类统计表")
print("=" * 80)
print(bidding_table.to_string(index=False))

print("\n" + "=" * 80)
print("优化后中标单位分类统计表")
print("=" * 80)
print(winning_table.to_string(index=False))

print(f"\n图表已生成:")
print(f"1. 招标单位综合分析图.png")
print(f"2. 中标单位综合分析图.png")
print(f"3. 优化后招标单位统计表.csv")
print(f"4. 优化后中标单位统计表.csv")

# 输出优化后的分析总结
print("\n" + "=" * 80)
print("优化后分析总结")
print("=" * 80)

print("\n【招标单位特点】:")
print(f"1. 国企主导: 国企招标项目{bidding_table.iloc[0]['项目数量']}个，占比{bidding_table.iloc[0]['占比(%)']}%，金额占比最高")
print(f"2. 低空经济公司活跃: 专业低空公司招标{bidding_table[bidding_table['招标单位分类']=='低空经济公司']['项目数量'].iloc[0]}个项目，体现产业专业化")
print(f"3. 政府引导明显: 政府招标{bidding_table[bidding_table['招标单位分类']=='政府']['项目数量'].iloc[0]}个项目，政策支持力度大")
print(f"4. 科技公司参与: 科技公司虽然项目数量不多，但平均金额较高，体现技术含量")

print("\n【中标单位特点】:")
print(f"1. 国企优势突出: 中标{winning_table.iloc[0]['项目数量']}个项目，占比{winning_table.iloc[0]['占比(%)']}%，金额占比达{(winning_table.iloc[0]['总金额(万元)']/winning_table['总金额(万元)'].sum()*100):.1f}%")
print(f"2. 科技公司崛起: 科技公司中标{winning_table[winning_table['中标单位分类']=='科技公司']['项目数量'].iloc[0]}个项目，成为重要参与者")
print(f"3. 专业化分工: 测绘单位、低空经济公司等专业机构各司其职")
print(f"4. 产业生态完善: 从国企到科技公司，从高校到专业机构，形成完整产业链")
