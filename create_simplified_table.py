import pandas as pd

# 读取数据
cities_data = pd.read_csv('三城市详细数据.csv', encoding='utf-8')
surveying_data = pd.read_csv('三城市测绘相关项目.csv', encoding='utf-8')
market_data = pd.read_csv('三城市市场规模对比.csv', encoding='utf-8')

# 创建精简分析表 - 只保留核心维度
summary_data = []

target_cities = ['苏州', '南京', '无锡']

for city in target_cities:
    # 基础市场数据
    city_market = market_data[market_data['城市'] == city].iloc[0]
    
    # 测绘项目数据
    city_surveying = surveying_data[surveying_data['城市'] == city]
    surveying_count = len(city_surveying)
    surveying_avg = city_surveying['中标价格_数值'].mean() if len(city_surveying) > 0 else 0
    
    # 项目类型分布
    city_projects = cities_data[cities_data['城市'] == city]
    service_pct = (city_projects['采购类型'] == '服务').sum() / len(city_projects) * 100
    
    # 项目规模分布
    def categorize_amount(amount):
        if pd.isna(amount):
            return '未知'
        if amount >= 1000:
            return '大型项目'
        elif amount >= 100:
            return '中型项目'
        else:
            return '小型项目'
    
    city_projects_copy = city_projects.copy()
    city_projects_copy['项目规模'] = city_projects_copy['中标价格_数值'].apply(categorize_amount)
    medium_large_pct = ((city_projects_copy['项目规模'] == '中型项目').sum() + 
                       (city_projects_copy['项目规模'] == '大型项目').sum()) / len(city_projects_copy) * 100
    
    # 主要中标单位
    top_winners = city_projects['中标单位'].value_counts().head(2)
    top_winner = f"{top_winners.index[0]}等" if len(top_winners) > 0 else ""
    
    # 市场定位
    if city == '苏州':
        market_position = "首选目标"
        advantages = "地理优势+规模最大"
    elif city == '南京':
        market_position = "重点拓展"
        advantages = "质量高+政策强"
    else:  # 无锡
        market_position = "潜力挖掘"
        advantages = "竞争缓和+潜力大"
    
    # 汇总数据 - 只保留核心维度
    row_data = {
        '总项目数(个)': int(city_market['项目数量']),
        '总金额(万元)': f"{city_market['总金额(万元)']:,.0f}",
        '平均金额(万元)': f"{city_market['平均金额(万元)']:.0f}",
        '测绘项目数(个)': surveying_count,
        '测绘平均金额(万元)': f"{surveying_avg:.0f}" if surveying_avg > 0 else "0",
        '服务类占比(%)': f"{service_pct:.0f}%",
        '中大型项目占比(%)': f"{medium_large_pct:.0f}%",
        '主要中标企业': top_winner,
        '竞争优势': advantages,
        '市场定位': market_position
    }
    
    summary_data.append(row_data)

# 创建DataFrame
summary_df = pd.DataFrame(summary_data, index=target_cities)

# 转置表格
transposed_df = summary_df.T
transposed_df.index.name = '分析维度'

# 保存为CSV
transposed_df.to_csv('三城市对比分析表_精简转置版.csv', encoding='utf-8-sig')

print("精简转置表已生成：三城市对比分析表_精简转置版.csv")
print("\n表格预览：")
print("="*80)
print(transposed_df.to_string())

print("\n" + "="*80)
print("从苏州角度的战略总结：")
print("="*80)

# 从苏州角度分析
suzhou_projects = int(market_data[market_data['城市'] == '苏州']['项目数量'].iloc[0])
suzhou_amount = market_data[market_data['城市'] == '苏州']['总金额(万元)'].iloc[0]
suzhou_surveying = len(surveying_data[surveying_data['城市'] == '苏州'])

total_projects = market_data['项目数量'].sum()
suzhou_share = suzhou_projects / total_projects * 100

summary_text = f"苏州低空经济市场规模领先三城市，占比{suzhou_share:.0f}%，测绘项目{suzhou_surveying}个，地理优势明显，应作为首选切入目标。"

print(f"总结（{len(summary_text)}字）：")
print(summary_text)

# 保存总结
with open('苏州市场总结.txt', 'w', encoding='utf-8') as f:
    f.write(summary_text)

print(f"\n总结已保存到：苏州市场总结.txt")
