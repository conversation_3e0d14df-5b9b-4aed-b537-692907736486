import pandas as pd

# 读取数据
cities_data = pd.read_csv('三城市详细数据.csv', encoding='utf-8')
surveying_data = pd.read_csv('三城市测绘相关项目.csv', encoding='utf-8')

print("=" * 80)
print("南京相比苏州的政策优势分析")
print("=" * 80)

# 分析南京和苏州的项目特点
nanjing_data = cities_data[cities_data['城市'] == '南京']
suzhou_data = cities_data[cities_data['城市'] == '苏州']

print("\n1. 政府层级优势分析")
print("-" * 50)

# 分析政府项目
def analyze_government_projects(city_data, city_name):
    gov_keywords = ['省', '市政府', '发改委', '交通局', '规划局', '交通运输局', '航空事业', '铁路与航空']
    
    gov_projects = []
    for _, project in city_data.iterrows():
        bidder = str(project['招标单位'])
        if any(keyword in bidder for keyword in gov_keywords):
            level = '省级' if '省' in bidder else '市级'
            gov_projects.append({
                '项目': project['项目标题'][:50] + '...',
                '招标单位': bidder,
                '层级': level,
                '金额': project['中标价格_数值']
            })
    
    return gov_projects

nanjing_gov = analyze_government_projects(nanjing_data, '南京')
suzhou_gov = analyze_government_projects(suzhou_data, '苏州')

print(f"南京政府项目分析:")
nj_provincial = [p for p in nanjing_gov if p['层级'] == '省级']
nj_municipal = [p for p in nanjing_gov if p['层级'] == '市级']

print(f"  省级项目: {len(nj_provincial)}个")
for project in nj_provincial:
    print(f"    • {project['招标单位']}: {project['金额']}万元")

print(f"  市级项目: {len(nj_municipal)}个")
for project in nj_municipal[:3]:  # 显示前3个
    print(f"    • {project['招标单位']}: {project['金额']}万元")

print(f"\n苏州政府项目分析:")
sz_provincial = [p for p in suzhou_gov if p['层级'] == '省级']
sz_municipal = [p for p in suzhou_gov if p['层级'] == '市级']

print(f"  省级项目: {len(sz_provincial)}个")
print(f"  市级项目: {len(sz_municipal)}个")

print("\n2. 政策规划项目对比")
print("-" * 50)

# 分析规划类项目
def analyze_planning_projects(city_data, city_name):
    planning_keywords = ['规划', '发展', '专项', '布局', '体系', '方案']
    
    planning_projects = []
    for _, project in city_data.iterrows():
        title = str(project['项目标题'])
        if any(keyword in title for keyword in planning_keywords):
            planning_projects.append({
                '项目': title,
                '招标单位': project['招标单位'],
                '金额': project['中标价格_数值'],
                '类型': '省级规划' if '省' in project['招标单位'] else '市级规划'
            })
    
    return planning_projects

nanjing_planning = analyze_planning_projects(nanjing_data, '南京')
suzhou_planning = analyze_planning_projects(suzhou_data, '苏州')

print(f"南京规划类项目: {len(nanjing_planning)}个")
for project in nanjing_planning:
    print(f"  • {project['项目'][:60]}...")
    print(f"    招标方: {project['招标单位']}")
    print(f"    金额: {project['金额']}万元")
    print()

print(f"苏州规划类项目: {len(suzhou_planning)}个")
for project in suzhou_planning[:3]:  # 显示前3个
    print(f"  • {project['项目'][:60]}...")
    print(f"    招标方: {project['招标单位']}")
    print(f"    金额: {project['金额']}万元")
    print()

print("\n3. 政策支持力度对比")
print("-" * 50)

# 分析政策支持相关的关键词
policy_keywords = ['发展', '支持', '建设', '推进', '促进', '扶持']

nanjing_policy_projects = []
suzhou_policy_projects = []

for _, project in nanjing_data.iterrows():
    title = str(project['项目标题'])
    if any(keyword in title for keyword in policy_keywords):
        nanjing_policy_projects.append(project)

for _, project in suzhou_data.iterrows():
    title = str(project['项目标题'])
    if any(keyword in title for keyword in policy_keywords):
        suzhou_policy_projects.append(project)

print(f"南京政策支持相关项目: {len(nanjing_policy_projects)}个")
print(f"苏州政策支持相关项目: {len(suzhou_policy_projects)}个")

print("\n4. 南京政策优势总结")
print("-" * 50)

advantages = [
    "省会城市地位：作为江苏省省会，南京在省级政策制定和资源配置中具有优先地位",
    "政策层级更高：拥有更多省级层面的低空经济发展规划和政策支持",
    "先行先试机会：省会城市通常是新政策的试点城市，享有政策创新优势",
    "资源整合能力：能够整合全省低空经济资源，统筹规划发展",
    "政策引导作用：南京的政策往往对全省其他城市具有示范和引导作用"
]

for i, advantage in enumerate(advantages, 1):
    print(f"{i}. {advantage}")

print("\n5. 具体政策优势体现")
print("-" * 50)

# 从项目数据中提取的政策优势
policy_evidences = [
    "江苏省低空经济发展专项规划：由省发改委主导，体现省级政策高度",
    "南京市低空空域分类划设：市级空域管理政策创新",
    "低空飞行服务中心建设：省市联动的基础设施政策支持",
    "航空产业集团参与：省属国企平台的政策资源优势",
    "多部门协同：交通、规划、航空等多部门政策协调"
]

for i, evidence in enumerate(policy_evidences, 1):
    print(f"{i}. {evidence}")

print("\n6. 对苏州园区测绘的启示")
print("-" * 50)

implications = [
    "关注南京省级政策动向：及时了解江苏省低空经济政策变化",
    "参与南京政策制定：通过行业协会等渠道参与政策讨论",
    "利用政策窗口期：抓住南京政策试点机会，积累经验后复制到苏州",
    "建立政策信息渠道：与南京相关部门建立信息沟通机制",
    "差异化定位：在苏州发挥市场化优势，在南京发挥政策敏感性"
]

for i, implication in enumerate(implications, 1):
    print(f"{i}. {implication}")

print("\n" + "=" * 80)
print("结论：南京的政策优势主要体现在省会地位带来的政策层级高、")
print("资源整合能力强、先行先试机会多等方面，这为低空经济发展")
print("提供了更强的政策支撑和发展空间。")
print("=" * 80)
